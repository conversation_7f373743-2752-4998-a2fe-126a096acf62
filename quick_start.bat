@echo off
title Quick Start Monitor

echo ========================================
echo Quick Start Monitor (Skip All Checks)
echo ========================================
echo.

if not exist ".venv\Scripts\python.exe" (
    echo ERROR: Virtual environment not found
    pause
    exit /b 1
)

echo Starting monitor without dependency checks...
echo.

:: Set environment variable to skip checks
set SKIP_DEPENDENCY_CHECK=1

.venv\Scripts\python.exe auto_restart.py

echo.
echo Monitor stopped
pause
