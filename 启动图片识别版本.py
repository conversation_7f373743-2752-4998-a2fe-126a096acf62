#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片识别增强版启动脚本
自动检查依赖并启动程序
"""

import sys
import subprocess
import importlib.util

def check_dependency(package_name, install_name=None):
    """检查依赖是否已安装"""
    if install_name is None:
        install_name = package_name
    
    spec = importlib.util.find_spec(package_name)
    return spec is not None

def install_dependency(package_name):
    """安装依赖包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        return True
    except subprocess.CalledProcessError:
        return False

def check_and_install_dependencies():
    """检查并安装必要的依赖"""
    print("🔍 检查依赖...")
    
    # 基础依赖
    basic_deps = [
        ("playwright", "playwright"),
        ("requests", "requests"),
        ("PIL", "Pillow")
    ]
    
    missing_deps = []
    
    for package, install_name in basic_deps:
        if not check_dependency(package):
            missing_deps.append(install_name)
    
    if missing_deps:
        print(f"❌ 缺少基础依赖: {', '.join(missing_deps)}")
        print("🚀 正在安装...")
        
        for dep in missing_deps:
            if install_dependency(dep):
                print(f"✅ {dep} 安装成功")
            else:
                print(f"❌ {dep} 安装失败")
                return False
    else:
        print("✅ 基础依赖已满足")
    
    # 检查OCR依赖
    ocr_available = False
    
    if check_dependency("paddleocr"):
        print("✅ PaddleOCR 可用")
        ocr_available = True
    elif check_dependency("pytesseract"):
        print("✅ Tesseract 可用")
        ocr_available = True
    else:
        print("⚠️ 未检测到OCR引擎")
        print("💡 建议运行: python install_ocr.py 安装OCR支持")
        
        choice = input("是否现在安装PaddleOCR? (y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            if install_dependency("paddleocr"):
                print("✅ PaddleOCR 安装成功")
                ocr_available = True
            else:
                print("❌ PaddleOCR 安装失败，将使用基础模式")
    
    return True, ocr_available

def show_feature_status(ocr_available):
    """显示功能状态"""
    print("\n" + "="*50)
    print("📋 功能状态:")
    print("✅ 基础答题功能: 已启用")
    print("✅ 智能匹配算法: 已启用")
    print("✅ HTML图片检测: 已启用")
    
    if ocr_available:
        print("✅ 图片OCR识别: 已启用")
        print("🎉 完整图片识别功能已就绪!")
    else:
        print("⚠️ 图片OCR识别: 未启用 (将使用基础回退模式)")
        print("💡 提示: 运行 'python install_ocr.py' 启用完整OCR功能")
    
    print("="*50)

def main():
    """主启动流程"""
    print("🎯 图片识别增强版启动器")
    print("="*50)
    
    # 检查并安装依赖
    deps_ok, ocr_available = check_and_install_dependencies()
    
    if not deps_ok:
        print("❌ 依赖安装失败，无法启动程序")
        return
    
    # 显示功能状态
    show_feature_status(ocr_available)
    
    # 询问是否运行测试
    if ocr_available:
        test_choice = input("\n是否先运行功能测试? (y/n): ").lower().strip()
        if test_choice in ['y', 'yes', '是']:
            print("\n🧪 运行功能测试...")
            try:
                import test_image_recognition
                test_image_recognition.run_all_tests()
            except Exception as e:
                print(f"❌ 测试失败: {e}")
            
            input("\n按回车键继续启动主程序...")
    
    # 启动主程序
    print("\n🚀 启动主程序...")
    try:
        import main
        # 这里可以添加主程序的启动逻辑
        print("✅ 程序已启动，请查看主程序窗口")
        print("💡 提示: 现在可以识别包含数学公式图片的题目了!")
        
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        print("请检查 main.py 文件是否存在")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"\n❌ 启动器出错: {e}")
        input("按回车键退出...")
