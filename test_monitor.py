#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控器测试脚本
用于测试自动重启功能
"""

import sys
import time
import random
from pathlib import Path

def test_normal_exit():
    """测试正常退出"""
    print("🧪 测试模式：正常退出")
    print("程序将在5秒后正常退出...")
    
    for i in range(5, 0, -1):
        print(f"倒计时: {i}")
        time.sleep(1)
    
    print("✅ 正常退出")
    sys.exit(0)

def test_crash_exit():
    """测试崩溃退出"""
    print("🧪 测试模式：模拟崩溃")
    print("程序将在3秒后崩溃...")
    
    for i in range(3, 0, -1):
        print(f"倒计时: {i}")
        time.sleep(1)
    
    print("💥 模拟崩溃")
    raise Exception("这是一个测试崩溃")

def test_random_behavior():
    """测试随机行为"""
    print("🧪 测试模式：随机行为")
    
    # 随机运行时间 (5-15秒)
    runtime = random.randint(5, 15)
    print(f"程序将运行 {runtime} 秒...")
    
    for i in range(runtime):
        print(f"运行中... {i+1}/{runtime}")
        time.sleep(1)
    
    # 随机选择退出方式
    if random.choice([True, False]):
        print("✅ 随机选择：正常退出")
        sys.exit(0)
    else:
        print("💥 随机选择：崩溃退出")
        raise Exception("随机崩溃测试")

def test_long_running():
    """测试长时间运行"""
    print("🧪 测试模式：长时间运行")
    print("程序将运行60秒，每10秒输出一次状态...")
    
    for i in range(6):
        print(f"📊 运行状态 {i+1}/6 - 已运行 {(i+1)*10} 秒")
        time.sleep(10)
    
    print("✅ 长时间运行测试完成")
    sys.exit(0)

def main():
    """主函数"""
    print("="*50)
    print("🧪 自动重启监控器测试程序")
    print("="*50)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        test_mode = sys.argv[1].lower()
    else:
        # 默认随机测试
        test_mode = "random"
    
    print(f"测试模式: {test_mode}")
    print()
    
    try:
        if test_mode == "normal":
            test_normal_exit()
        elif test_mode == "crash":
            test_crash_exit()
        elif test_mode == "long":
            test_long_running()
        elif test_mode == "random":
            test_random_behavior()
        else:
            print(f"❌ 未知测试模式: {test_mode}")
            print("可用模式: normal, crash, long, random")
            sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n⚠️ 收到键盘中断，退出测试")
        sys.exit(0)
    except Exception as e:
        print(f"💥 程序崩溃: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
