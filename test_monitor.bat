@echo off
title Test Auto Restart Monitor

echo.
echo ========================================
echo Test Auto Restart Monitor
echo ========================================
echo.

:: Check test script
if not exist "test_monitor.py" (
    echo ERROR: Test script not found
    pause
    exit /b 1
)

:: Backup original main.py
if exist "main.py" (
    echo Backing up original main.py...
    copy "main.py" "main.py.backup" >nul
    echo Backed up as main.py.backup
)

:: Copy test script as main.py
echo Using test script to replace main.py...
copy "test_monitor.py" "main.py" >nul

echo.
echo Test Options:
echo    1. Normal exit test (normal)
echo    2. Crash exit test (crash)
echo    3. Long running test (long)
echo    4. Random behavior test (random)
echo.

set /p choice="Please select test mode (1-4): "

if "%choice%"=="1" (
    set test_mode=normal
) else if "%choice%"=="2" (
    set test_mode=crash
) else if "%choice%"=="3" (
    set test_mode=long
) else if "%choice%"=="4" (
    set test_mode=random
) else (
    set test_mode=random
    echo Using default mode: random
)

echo.
echo Starting monitor test (mode: %test_mode%)...
echo.
echo Tips:
echo    - Watch how monitor handles program exit/crash
echo    - Press Ctrl+C or run stop_monitor.bat to stop test
echo    - Original main.py will be restored after test
echo.

:: Modify test script, add test mode parameter
echo import sys > temp_main.py
echo sys.argv.append('%test_mode%') >> temp_main.py
type test_monitor.py >> temp_main.py
move temp_main.py main.py >nul

:: Start monitor
.venv\Scripts\python.exe auto_restart.py

:: Restore original files after test
echo.
echo Restoring original files...
if exist "main.py.backup" (
    move "main.py.backup" "main.py" >nul
    echo Original main.py restored
) else (
    del "main.py" >nul 2>&1
    echo Test file deleted, please manually restore main.py
)

echo.
echo Test completed
pause
