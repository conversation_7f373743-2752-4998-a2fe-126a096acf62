#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片识别功能测试脚本
测试OCR识别能力和混合内容处理
"""

import re
from ocr_handler import OCRHandler, 识别图片内容
from main import 检测图片内容, 获取混合内容, 提取答案选项, 智能匹配答案

def test_html_image_detection():
    """测试HTML图片检测功能"""
    print("🧪 测试HTML图片检测功能")
    print("-" * 40)
    
    # 模拟包含图片的HTML内容
    test_html = '''
    <div class="everyOption answerSort">
        <div class="optionIndex">A</div>
        <div class="selectContent">
            <p><img src="https://zzx-idcard-pic.ouc-online.com.cn:443/zzximg/20190730/1564476221305162.png?AccessKeyId=W7RGM3ZQP2DHJIR1LFBK&Expires=1755099540&Signature=B5jZxDvZJh31KX26rVWy9I9bALY%3D" title="1564476221305162.png" alt="图片99.png">的值域都是R</p>
        </div>
    </div>
    '''
    
    # 测试图片URL检测
    img_urls = 检测图片内容(test_html)
    
    print(f"检测到的图片数量: {len(img_urls)}")
    for i, url in enumerate(img_urls, 1):
        print(f"图片{i}: {url}")
    
    return len(img_urls) > 0

def test_ocr_engines():
    """测试OCR引擎可用性"""
    print("\n🧪 测试OCR引擎可用性")
    print("-" * 40)
    
    ocr_handler = OCRHandler()
    
    print(f"可用引擎: {ocr_handler.available_engines}")
    
    # 测试一个示例图片URL（如果可用）
    test_url = "https://zzx-idcard-pic.ouc-online.com.cn:443/zzximg/20190730/1564476221305162.png"
    
    print(f"\n测试图片识别: {test_url}")
    result = 识别图片内容(test_url)
    print(f"识别结果: {result}")
    
    return result is not None

def test_answer_extraction():
    """测试答案提取功能"""
    print("\n🧪 测试答案提取功能")
    print("-" * 40)
    
    # 测试不同类型的答案文本
    test_cases = [
        "A\n\n的值域都是R",
        "B\n\n[数学公式_1564476221305162]",
        "C\n\n函数f(x)=x²",
        "D\n\n(-∞,+∞)",
        "A\n\n[图片内容] 是正确的"
    ]
    
    for i, test_text in enumerate(test_cases, 1):
        print(f"\n测试用例{i}: {repr(test_text)}")
        extracted = 提取答案选项(test_text)
        print(f"提取结果: {extracted}")

def test_smart_matching():
    """测试智能匹配功能"""
    print("\n🧪 测试智能匹配功能")
    print("-" * 40)
    
    # 模拟答案选项（包含OCR识别结果）
    答案选项 = ["的值域都是R", "[数学公式_1564476221305162]"]
    
    # 模拟页面选项（包含图片识别结果）
    页面选项 = [
        "函数f(x)=log₂x 的值域都是R",
        "函数g(x)=2^x 的定义域是R", 
        "函数h(x)=√x 的值域是[0,+∞)",
        "函数k(x)=1/x 的定义域是(-∞,0)∪(0,+∞)"
    ]
    
    print("答案选项:", 答案选项)
    print("页面选项:", 页面选项)
    
    匹配结果, 匹配原因 = 智能匹配答案(答案选项, 页面选项)
    
    if 匹配结果is not None:
        print(f"✅ 匹配成功: 选项{匹配结果 + 1}")
        print(f"匹配原因: {匹配原因}")
        print(f"匹配的选项: {页面选项[匹配结果]}")
    else:
        print(f"❌ 匹配失败: {匹配原因}")

def test_mixed_content_simulation():
    """模拟混合内容处理"""
    print("\n🧪 模拟混合内容处理")
    print("-" * 40)
    
    # 模拟从页面获取的混合内容
    original_text = "A\n\n的值域都是R"
    img_urls = ["https://zzx-idcard-pic.ouc-online.com.cn:443/zzximg/20190730/1564476221305162.png"]
    
    print(f"原始文本: {repr(original_text)}")
    print(f"图片URLs: {img_urls}")
    
    # 模拟OCR识别结果
    ocr_results = []
    for url in img_urls:
        # 这里使用模拟结果，实际会调用OCR
        mock_ocr_result = "f(x)=log₂x"
        ocr_results.append(mock_ocr_result)
        print(f"模拟OCR结果: {mock_ocr_result}")
    
    # 合并内容
    combined_content = original_text
    if ocr_results:
        combined_content += " " + " ".join(ocr_results)
    
    print(f"合并后内容: {repr(combined_content)}")
    
    # 提取答案选项
    extracted_options = 提取答案选项(combined_content)
    print(f"提取的选项: {extracted_options}")

def run_all_tests():
    """运行所有测试"""
    print("🎯 图片识别功能测试套件")
    print("=" * 50)
    
    tests = [
        ("HTML图片检测", test_html_image_detection),
        ("OCR引擎测试", test_ocr_engines),
        ("答案提取测试", test_answer_extraction),
        ("智能匹配测试", test_smart_matching),
        ("混合内容模拟", test_mixed_content_simulation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            result = test_func()
            results.append((test_name, "✅ 通过" if result else "⚠️ 部分通过"))
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results.append((test_name, f"❌ 失败: {e}"))
    
    # 输出测试总结
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    for test_name, result in results:
        print(f"  {test_name}: {result}")
    
    print("\n💡 提示:")
    print("- 如果OCR测试失败，请运行 python install_ocr.py 安装依赖")
    print("- 网络问题可能影响图片下载测试")
    print("- 实际使用时效果可能因图片质量而异")

if __name__ == "__main__":
    run_all_tests()
