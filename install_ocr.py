#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR依赖安装脚本
自动安装PaddleOCR或Tesseract等OCR库
"""

import subprocess
import sys
import os

def run_command(command):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def install_paddleocr():
    """安装PaddleOCR"""
    print("🚀 正在安装PaddleOCR...")
    
    # 安装PaddleOCR
    success, stdout, stderr = run_command("pip install paddleocr")
    
    if success:
        print("✅ PaddleOCR安装成功！")
        return True
    else:
        print(f"❌ PaddleOCR安装失败: {stderr}")
        return False

def install_tesseract():
    """安装Tesseract OCR"""
    print("🚀 正在安装Tesseract OCR...")
    
    # 安装Python包
    success, stdout, stderr = run_command("pip install pytesseract")
    
    if success:
        print("✅ pytesseract Python包安装成功！")
        
        # 检查系统是否已安装Tesseract
        test_success, _, _ = run_command("tesseract --version")
        
        if test_success:
            print("✅ Tesseract系统程序已安装！")
            return True
        else:
            print("⚠️ 需要手动安装Tesseract系统程序:")
            print("   Windows: 下载 https://github.com/UB-Mannheim/tesseract/wiki")
            print("   macOS: brew install tesseract")
            print("   Ubuntu: sudo apt install tesseract-ocr tesseract-ocr-chi-sim")
            return False
    else:
        print(f"❌ pytesseract安装失败: {stderr}")
        return False

def install_basic_dependencies():
    """安装基础依赖"""
    print("🚀 正在安装基础依赖...")
    
    dependencies = [
        "requests",
        "Pillow"
    ]
    
    for dep in dependencies:
        print(f"安装 {dep}...")
        success, stdout, stderr = run_command(f"pip install {dep}")
        
        if success:
            print(f"✅ {dep} 安装成功")
        else:
            print(f"❌ {dep} 安装失败: {stderr}")
            return False
    
    return True

def test_ocr_installation():
    """测试OCR安装"""
    print("\n🧪 测试OCR安装...")
    
    # 测试PaddleOCR
    try:
        import paddleocr
        print("✅ PaddleOCR 可用")
        paddleocr_available = True
    except ImportError:
        print("❌ PaddleOCR 不可用")
        paddleocr_available = False
    
    # 测试Tesseract
    try:
        import pytesseract
        # 简单测试
        test_success, _, _ = run_command("tesseract --version")
        if test_success:
            print("✅ Tesseract 可用")
            tesseract_available = True
        else:
            print("❌ Tesseract 系统程序不可用")
            tesseract_available = False
    except ImportError:
        print("❌ pytesseract 不可用")
        tesseract_available = False
    
    return paddleocr_available, tesseract_available

def main():
    """主安装流程"""
    print("🎯 OCR依赖安装向导")
    print("=" * 50)
    
    # 安装基础依赖
    if not install_basic_dependencies():
        print("❌ 基础依赖安装失败，请检查网络连接")
        return
    
    print("\n选择要安装的OCR引擎:")
    print("1. PaddleOCR (推荐，支持中文和数学公式)")
    print("2. Tesseract (轻量级，需要额外安装系统程序)")
    print("3. 两个都安装")
    print("4. 跳过OCR安装（仅使用基础功能）")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == "1":
        install_paddleocr()
    elif choice == "2":
        install_tesseract()
    elif choice == "3":
        install_paddleocr()
        install_tesseract()
    elif choice == "4":
        print("⏭️ 跳过OCR安装")
    else:
        print("❌ 无效选择")
        return
    
    # 测试安装结果
    paddleocr_ok, tesseract_ok = test_ocr_installation()
    
    print("\n" + "=" * 50)
    print("📋 安装总结:")
    
    if paddleocr_ok:
        print("✅ PaddleOCR 已就绪")
    if tesseract_ok:
        print("✅ Tesseract 已就绪")
    
    if paddleocr_ok or tesseract_ok:
        print("🎉 OCR功能已启用！现在可以识别图片中的数学公式了")
    else:
        print("⚠️ 没有可用的OCR引擎，将使用基础回退模式")
    
    print("\n💡 提示:")
    print("- PaddleOCR首次使用时会下载模型文件")
    print("- 如果遇到问题，请检查网络连接和Python环境")
    print("- 可以随时重新运行此脚本来安装缺失的组件")

if __name__ == "__main__":
    main()
