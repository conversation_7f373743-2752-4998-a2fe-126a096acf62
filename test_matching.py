#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能匹配算法的脚本
"""

import re
import difflib
from typing import List, Tuple, Optional

def 清理文本(text: str) -> str:
    """清理文本，去除多余的空格、换行符和标点符号"""
    if not text:
        return ""
    
    # 去除首尾空格和换行符
    cleaned = text.strip()
    
    # 将多个连续空格替换为单个空格
    cleaned = re.sub(r'\s+', ' ', cleaned)
    
    # 去除常见的标点符号，特别处理顿号和逗号
    cleaned = re.sub(r'[。、，；：！？""''（）【】《》〈〉\s]', '', cleaned)
    
    return cleaned.strip()

def 提取答案选项(answer_text: str) -> List[str]:
    """从答案文本中提取可能的选项，避免重复和选项字母"""
    if not answer_text:
        return []
    
    # 尝试按常见分隔符分割
    options = []
    
    # 按换行符分割
    lines = answer_text.split('\n')
    for line in lines:
        line = line.strip()
        if line and len(line) > 2:
            # 去除选项字母前缀 (A、B、C、D等)
            cleaned_line = re.sub(r'^[A-D][、\s\.]\s*', '', line)
            if cleaned_line:
                options.append(清理文本(cleaned_line))
    
    # 如果没有找到多行，尝试按其他分隔符
    if len(options) <= 1:
        # 按句号分割
        parts = answer_text.split('。')
        for part in parts:
            part = part.strip()
            if part and len(part) > 2:
                # 去除选项字母前缀
                cleaned_part = re.sub(r'^[A-D][、\s\.]\s*', '', part)
                if cleaned_part:
                    options.append(清理文本(cleaned_part))
    
    # 去重并过滤，避免重复选项
    unique_options = []
    for opt in options:
        if opt and len(opt) > 2:
            # 检查是否已存在相似选项
            is_duplicate = False
            for existing in unique_options:
                if opt in existing or existing in opt or difflib.SequenceMatcher(None, opt, existing).ratio() > 0.9:
                    is_duplicate = True
                    break
            if not is_duplicate:
                unique_options.append(opt)
    
    return unique_options

def 智能匹配答案(答案选项列表: List[str], 页面选项列表: List[str]) -> Tuple[Optional[int], str]:
    """
    智能匹配答案，返回匹配的选项索引和匹配原因
    返回: (选项索引(0-based), 匹配原因)
    """
    if not 答案选项列表 or not 页面选项列表:
        return None, "答案或选项列表为空"
    
    print(f"🔍 开始智能匹配...")
    print(f"答案选项: {答案选项列表}")
    print(f"页面选项: {页面选项列表}")
    
    # 策略1: 精确匹配（清理后）
    for i, 页面选项 in enumerate(页面选项列表):
        清理后页面选项 = 清理文本(页面选项)
        for 答案选项 in 答案选项列表:
            if 清理后页面选项 == 答案选项:
                return i, f"精确匹配: '{答案选项}'"
    
    # 策略2: 包含匹配（答案包含在选项中）
    for i, 页面选项 in enumerate(页面选项列表):
        清理后页面选项 = 清理文本(页面选项)
        for 答案选项 in 答案选项列表:
            if 答案选项 in 清理后页面选项 or 清理后页面选项 in 答案选项:
                return i, f"包含匹配: 答案'{答案选项}' <-> 选项'{清理后页面选项}'"
    
    # 策略3: 关键词匹配（提取关键词进行匹配）
    for i, 页面选项 in enumerate(页面选项列表):
        清理后页面选项 = 清理文本(页面选项)
        for 答案选项 in 答案选项列表:
            # 提取长度大于2的词作为关键词
            答案关键词 = [word for word in re.findall(r'[\u4e00-\u9fff]+', 答案选项) if len(word) > 2]
            选项关键词 = [word for word in re.findall(r'[\u4e00-\u9fff]+', 清理后页面选项) if len(word) > 2]
            
            # 检查是否有共同关键词
            共同关键词 = set(答案关键词) & set(选项关键词)
            if 共同关键词 and len(共同关键词) >= 1:
                return i, f"关键词匹配: 共同关键词 {共同关键词}"
    
    # 策略4: 相似度匹配
    最高相似度 = 0
    最佳匹配索引 = None
    最佳匹配信息 = ""
    
    for i, 页面选项 in enumerate(页面选项列表):
        清理后页面选项 = 清理文本(页面选项)
        for 答案选项 in 答案选项列表:
            相似度 = difflib.SequenceMatcher(None, 答案选项, 清理后页面选项).ratio()
            if 相似度 > 最高相似度:
                最高相似度 = 相似度
                最佳匹配索引 = i
                最佳匹配信息 = f"相似度匹配: {相似度:.2f} - 答案'{答案选项}' <-> 选项'{清理后页面选项}'"
    
    # 如果相似度大于0.6，认为是有效匹配
    if 最高相似度 > 0.6:
        return 最佳匹配索引, 最佳匹配信息
    
    return None, f"所有匹配策略都失败，最高相似度: {最高相似度:.2f}"

def test_matching():
    """测试匹配算法"""
    print("🧪 开始测试智能匹配算法\n")
    
    # 测试用例1: 标点符号差异
    print("=" * 50)
    print("测试用例1: 标点符号差异")
    答案1 = ["生理心理及社交"]
    选项1 = ["生理、心理及社交", "身体健康", "心理健康", "社交健康"]
    结果1, 原因1 = 智能匹配答案(答案1, 选项1)
    print(f"结果: 选项{结果1 + 1 if 结果1 is not None else '无'} - {原因1}\n")
    
    # 测试用例2: 重复答案处理
    print("=" * 50)
    print("测试用例2: 重复答案处理")
    原始答案2 = "危险源和隐患不需要区分\nD 危险源和隐患不需要区分"
    答案2 = 提取答案选项(原始答案2)
    选项2 = ["需要严格区分", "部分需要区分", "根据情况区分", "危险源和隐患不需要区分"]
    print(f"提取的答案: {答案2}")
    结果2, 原因2 = 智能匹配答案(答案2, 选项2)
    print(f"结果: 选项{结果2 + 1 if 结果2 is not None else '无'} - {原因2}\n")
    
    # 测试用例3: WHO定义匹配
    print("=" * 50)
    print("测试用例3: WHO定义匹配")
    答案3 = ["保护和促进工人的身体健康预防职业病和工伤事故的发生"]
    选项3 = [
        "保护和促进工人的身体健康，预防职业病和工伤事故的发生",
        "确保工作场所安全",
        "提高工作效率",
        "降低生产成本"
    ]
    结果3, 原因3 = 智能匹配答案(答案3, 选项3)
    print(f"结果: 选项{结果3 + 1 if 结果3 is not None else '无'} - {原因3}\n")

if __name__ == "__main__":
    test_matching()
