#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本 - 跳过所有检查直接启动监控器
适用于确认环境正常的情况
"""

import sys
import subprocess
from pathlib import Path

def main():
    """快速启动主函数"""
    print("🚀 快速启动监控器（跳过环境检查）")
    print("="*50)
    
    script_dir = Path(__file__).parent
    venv_python = script_dir / ".venv" / "Scripts" / "python.exe"
    
    # 基本检查
    if not venv_python.exists():
        print("❌ 虚拟环境不存在")
        input("按回车键退出...")
        return
    
    # 设置环境变量跳过检查
    import os
    os.environ["SKIP_DEPENDENCY_CHECK"] = "1"
    
    # 启动监控器
    try:
        print("启动监控器...")
        subprocess.run([str(venv_python), "auto_restart.py"], cwd=str(script_dir))
    except KeyboardInterrupt:
        print("\n监控器已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
