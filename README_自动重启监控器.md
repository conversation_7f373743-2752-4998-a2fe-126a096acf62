# 🎯 自动重启监控器使用指南

## 📋 功能概述

这个自动重启监控器专为你的 `main.py` 程序设计，提供以下功能：

- ✅ **自动重启**：程序崩溃时自动重启
- ✅ **虚拟环境支持**：在 `.venv` 虚拟环境中运行
- ✅ **智能监控**：实时监控程序状态和输出
- ✅ **日志记录**：详细的运行日志和错误记录
- ✅ **优雅停止**：支持手动停止和信号处理
- ✅ **配置灵活**：可自定义监控参数
- ✅ **环境检查**：启动前检查依赖和环境
- ✅ **临时文件清理**：重启前自动清理临时文件

## 🚀 快速开始

### 1. 启动监控器

**方法一：使用批处理脚本（推荐）**
```bash
# 双击运行或在命令行执行
start_monitor.bat
```

**方法二：直接运行Python脚本**
```bash
# 在虚拟环境中运行
.venv\Scripts\python.exe auto_restart.py
```

### 2. 停止监控器

**方法一：使用停止脚本**
```bash
# 双击运行
stop_monitor.bat
```

**方法二：手动创建停止标志**
```bash
# 创建停止标志文件
echo. > stop_monitor.flag
```

**方法三：键盘中断**
```bash
# 在监控器窗口按 Ctrl+C
```

## 📁 文件说明

| 文件名 | 说明 |
|--------|------|
| `auto_restart.py` | 主监控脚本 |
| `start_monitor.bat` | 启动脚本（Windows） |
| `stop_monitor.bat` | 停止脚本（Windows） |
| `monitor_config.py` | 配置文件 |
| `logs/` | 日志目录 |
| `stop_monitor.flag` | 停止标志文件 |

## ⚙️ 配置说明

编辑 `monitor_config.py` 可以自定义以下参数：

### 监控配置
```python
MONITOR_CONFIG = {
    "max_restarts": 10,        # 最大重启次数
    "restart_delay": 5,        # 重启延迟（秒）
    "check_interval": 0.1,     # 进程检查间隔（秒）
    "show_main_output": True,  # 是否显示主程序输出
    "log_level": "INFO",       # 日志级别
}
```

### 路径配置
```python
PATH_CONFIG = {
    "main_script": "main.py",                    # 主脚本名称
    "venv_python": ".venv/Scripts/python.exe",  # 虚拟环境Python路径
    "log_dir": "logs",                          # 日志目录
    "stop_flag": "stop_monitor.flag",           # 停止标志文件
}
```

### 高级配置
```python
ADVANCED_CONFIG = {
    "cleanup_temp_files": True,                           # 是否清理临时文件
    "temp_dirs": ["temp_images", "__pycache__"],         # 临时文件目录
    "check_dependencies": True,                           # 是否检查依赖
    "required_packages": ["playwright", "requests"],     # 必需依赖包
}
```

## 📊 监控状态

监控器运行时会显示：

```
📊 监控状态
   运行时间: 0:05:23
   重启次数: 2/10
   日志目录: G:\project\Kucc\logs
   停止方法: 创建文件 stop_monitor.flag 或按 Ctrl+C
```

## 📝 日志系统

### 日志文件位置
- **监控日志**：`logs/monitor_YYYYMMDD.log`
- **主程序输出**：集成在监控日志中

### 日志级别
- `INFO`：正常运行信息
- `WARNING`：警告信息（如重启）
- `ERROR`：错误信息（如启动失败）

### 日志示例
```
2024-01-15 10:30:15 - INFO - 🎯 自动重启监控器启动
2024-01-15 10:30:15 - INFO - 🔍 检查运行环境...
2024-01-15 10:30:15 - INFO - ✅ 环境检查通过
2024-01-15 10:30:16 - INFO - 🚀 启动主程序...
2024-01-15 10:30:16 - INFO - ✅ 主程序已启动，PID: 12345
2024-01-15 10:30:17 - INFO - [主程序] 程序正在运行...
```

## 🔧 故障排除

### 常见问题

**Q: 监控器启动失败？**
A: 检查以下项目：
1. 虚拟环境是否存在：`.venv/Scripts/python.exe`
2. 主脚本是否存在：`main.py`
3. 依赖是否安装完整

**Q: 程序重启次数过多？**
A: 
1. 检查 `main.py` 中的错误
2. 查看日志文件了解崩溃原因
3. 调整 `max_restarts` 参数

**Q: 如何查看详细错误信息？**
A:
1. 查看 `logs/` 目录中的日志文件
2. 设置 `log_level` 为 `DEBUG`
3. 启用 `show_main_output` 查看主程序输出

**Q: 监控器无法停止？**
A:
1. 运行 `stop_monitor.bat`
2. 手动创建 `stop_monitor.flag` 文件
3. 使用任务管理器结束进程

### 调试模式

如需调试，可以修改配置：

```python
MONITOR_CONFIG = {
    "log_level": "DEBUG",      # 启用调试日志
    "show_main_output": True,  # 显示主程序输出
    "check_interval": 1.0,     # 增加检查间隔
}
```

## 🎯 最佳实践

1. **定期检查日志**：了解程序运行状况
2. **合理设置重启次数**：避免无限重启
3. **监控系统资源**：确保有足够的内存和CPU
4. **备份重要数据**：程序重启可能丢失内存中的数据
5. **测试停止机制**：确保能够正常停止监控

## 📞 技术支持

如果遇到问题：
1. 查看日志文件获取详细信息
2. 检查配置文件是否正确
3. 确认虚拟环境和依赖完整
4. 测试主程序是否能独立运行

---

**提示**：这个监控器专为你的 Playwright 自动化项目设计，支持图片识别功能和虚拟环境运行。
