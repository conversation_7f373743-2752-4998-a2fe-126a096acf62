#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动重启监控脚本
监控main.py程序，崩溃时自动重启
支持虚拟环境运行
"""

import os
import sys
import time
import subprocess
import signal
import logging
import shutil
from datetime import datetime
from pathlib import Path

# 尝试导入配置文件
try:
    from monitor_config import get_config
    CONFIG = get_config()
except ImportError:
    # 如果配置文件不存在，使用默认配置
    CONFIG = {
        "monitor": {
            "max_restarts": 10,
            "restart_delay": 5,
            "check_interval": 0.1,
            "show_main_output": True,
            "log_level": "INFO",
            "save_main_output": True,
        },
        "path": {
            "main_script": "main.py",
            "venv_python": ".venv/Scripts/python.exe",
            "log_dir": "logs",
            "stop_flag": "stop_monitor.flag",
        },
        "advanced": {
            "cleanup_temp_files": True,
            "temp_dirs": ["temp_images", "__pycache__"],
            "check_dependencies": True,
            "required_packages": ["playwright", "requests", "Pillow"],
        }
    }

class AutoRestartMonitor:
    def __init__(self):
        self.script_dir = Path(__file__).parent
        self.config = CONFIG

        # 从配置文件读取路径
        self.main_script = self.script_dir / self.config["path"]["main_script"]
        self.venv_python = self.script_dir / self.config["path"]["venv_python"]
        self.log_dir = self.script_dir / self.config["path"]["log_dir"]
        self.stop_file = self.script_dir / self.config["path"]["stop_flag"]

        # 从配置文件读取监控参数
        self.max_restarts = self.config["monitor"]["max_restarts"]
        self.restart_delay = self.config["monitor"]["restart_delay"]
        self.check_interval = self.config["monitor"]["check_interval"]
        self.show_main_output = self.config["monitor"]["show_main_output"]
        self.save_main_output = self.config["monitor"]["save_main_output"]

        # 运行时状态
        self.restart_count = 0
        self.start_time = datetime.now()
        
        # 确保日志目录存在
        self.log_dir.mkdir(exist_ok=True)
        
        # 配置日志
        self.setup_logging()
        
        # 信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def setup_logging(self):
        """配置日志系统"""
        log_file = self.log_dir / f"monitor_{datetime.now().strftime('%Y%m%d')}.log"
        
        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def signal_handler(self, signum, frame):
        """处理停止信号"""
        self.logger.info(f"收到停止信号 {signum}，正在优雅退出...")
        self.create_stop_flag()
        sys.exit(0)
    
    def create_stop_flag(self):
        """创建停止标志文件"""
        try:
            self.stop_file.touch()
            self.logger.info("已创建停止标志文件")
        except Exception as e:
            self.logger.error(f"创建停止标志文件失败: {e}")
    
    def should_stop(self):
        """检查是否应该停止监控"""
        return self.stop_file.exists()
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        if not self.config["advanced"]["cleanup_temp_files"]:
            return

        self.logger.info("🧹 清理临时文件...")
        for temp_dir in self.config["advanced"]["temp_dirs"]:
            temp_path = self.script_dir / temp_dir
            if temp_path.exists():
                try:
                    if temp_path.is_dir():
                        shutil.rmtree(temp_path)
                        self.logger.info(f"✅ 已清理目录: {temp_dir}")
                    else:
                        temp_path.unlink()
                        self.logger.info(f"✅ 已清理文件: {temp_dir}")
                except Exception as e:
                    self.logger.warning(f"⚠️ 清理失败 {temp_dir}: {e}")

    def check_dependencies(self):
        """检查Python依赖"""
        # 检查环境变量是否跳过依赖检查
        if os.environ.get("SKIP_DEPENDENCY_CHECK") == "1":
            self.logger.info("⏭️ 跳过依赖检查（环境变量设置）")
            return True

        if not self.config["advanced"]["check_dependencies"]:
            return True

        self.logger.info("📦 检查Python依赖...")
        missing_packages = []

        # 包名映射（导入名 -> 包名）
        package_mapping = {
            "PIL": "Pillow",
            "playwright": "playwright",
            "requests": "requests"
        }

        for package in self.config["advanced"]["required_packages"]:
            # 获取实际的导入名
            import_name = package
            if package == "Pillow":
                import_name = "PIL"

            try:
                cmd = [str(self.venv_python), "-c", f"import {import_name}; print('OK')"]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                if result.returncode != 0:
                    self.logger.debug(f"依赖检查失败 {package}: {result.stderr}")
                    missing_packages.append(package)
                else:
                    self.logger.debug(f"依赖检查通过: {package}")
            except Exception as e:
                self.logger.warning(f"⚠️ 检查依赖 {package} 时出错: {e}")
                missing_packages.append(package)

        if missing_packages:
            self.logger.warning(f"⚠️ 缺少依赖: {', '.join(missing_packages)}")
            self.logger.info("💡 提示: 可以运行 pip install -r requirements.txt 安装依赖")

            # 询问是否继续运行
            self.logger.info("🤔 是否忽略依赖检查继续运行？程序可能仍然可以正常工作...")
            return False  # 暂时返回False，稍后我们会添加跳过选项

        self.logger.info("✅ 依赖检查通过")
        return True

    def check_environment(self):
        """检查运行环境"""
        self.logger.info("🔍 检查运行环境...")

        # 检查主脚本
        if not self.main_script.exists():
            self.logger.error(f"❌ 主脚本不存在: {self.main_script}")
            return False

        # 检查虚拟环境
        if not self.venv_python.exists():
            self.logger.error(f"❌ 虚拟环境Python不存在: {self.venv_python}")
            return False

        # 检查依赖（允许跳过）
        if not self.check_dependencies():
            self.logger.warning("⚠️ 依赖检查失败，但将继续运行...")
            self.logger.info("💡 如果程序运行出错，请检查并安装缺失的依赖")

        self.logger.info("✅ 环境检查完成")
        return True
    
    def run_main_script(self):
        """运行主脚本"""
        try:
            self.logger.info("🚀 启动主程序...")
            
            # 构建命令
            cmd = [str(self.venv_python), str(self.main_script)]
            
            # 启动进程
            process = subprocess.Popen(
                cmd,
                cwd=str(self.script_dir),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.logger.info(f"✅ 主程序已启动，PID: {process.pid}")
            return process
            
        except Exception as e:
            self.logger.error(f"❌ 启动主程序失败: {e}")
            return None
    
    def monitor_process(self, process):
        """监控进程状态"""
        try:
            # 实时读取输出
            while True:
                if self.should_stop():
                    self.logger.info("检测到停止标志，终止主程序...")
                    process.terminate()
                    break
                
                # 检查进程状态
                return_code = process.poll()
                if return_code is not None:
                    # 进程已结束
                    self.logger.warning(f"⚠️ 主程序已退出，返回码: {return_code}")
                    return return_code
                
                # 读取输出（非阻塞）
                try:
                    output = process.stdout.readline()
                    if output and self.show_main_output:
                        # 将主程序输出写入日志
                        self.logger.info(f"[主程序] {output.strip()}")
                except:
                    pass

                time.sleep(self.check_interval)  # 使用配置的检查间隔
            
            return 0  # 手动停止
            
        except Exception as e:
            self.logger.error(f"❌ 监控进程时出错: {e}")
            return -1
    
    def should_restart(self, return_code):
        """判断是否应该重启"""
        if self.should_stop():
            return False
        
        if self.restart_count >= self.max_restarts:
            self.logger.error(f"❌ 已达到最大重启次数 {self.max_restarts}，停止监控")
            return False
        
        if return_code == 0:
            self.logger.info("✅ 主程序正常退出，不需要重启")
            return False
        
        return True
    
    def print_status(self):
        """打印运行状态"""
        runtime = datetime.now() - self.start_time
        print("\n" + "="*60)
        print(f"📊 监控状态")
        print(f"   运行时间: {runtime}")
        print(f"   重启次数: {self.restart_count}/{self.max_restarts}")
        print(f"   日志目录: {self.log_dir}")
        print(f"   停止方法: 创建文件 {self.stop_file.name} 或按 Ctrl+C")
        print("="*60 + "\n")
    
    def run(self):
        """主监控循环"""
        self.logger.info("🎯 自动重启监控器启动")
        self.logger.info(f"   主脚本: {self.main_script}")
        self.logger.info(f"   虚拟环境: {self.venv_python}")
        
        # 检查环境
        if not self.check_environment():
            return False
        
        # 清理停止标志
        if self.stop_file.exists():
            self.stop_file.unlink()
        
        try:
            while True:
                if self.should_stop():
                    self.logger.info("检测到停止标志，退出监控")
                    break
                
                # 打印状态
                self.print_status()
                
                # 启动主程序
                process = self.run_main_script()
                if not process:
                    self.logger.error("启动失败，等待后重试...")
                    time.sleep(self.restart_delay)
                    continue
                
                # 监控进程
                return_code = self.monitor_process(process)
                
                # 判断是否需要重启
                if not self.should_restart(return_code):
                    break
                
                # 准备重启
                self.restart_count += 1
                self.logger.warning(f"🔄 准备重启 ({self.restart_count}/{self.max_restarts})...")

                # 清理临时文件
                self.cleanup_temp_files()

                self.logger.info(f"⏰ 等待 {self.restart_delay} 秒后重启...")
                time.sleep(self.restart_delay)
        
        except KeyboardInterrupt:
            self.logger.info("收到键盘中断，正在退出...")
        except Exception as e:
            self.logger.error(f"监控过程中出现错误: {e}")
        
        self.logger.info("🏁 监控器已停止")
        return True

def main():
    """主函数"""
    print("🎯 自动重启监控器")
    print("="*50)
    
    monitor = AutoRestartMonitor()
    monitor.run()

if __name__ == "__main__":
    main()
