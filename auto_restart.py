#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动重启监控脚本
监控main.py程序，崩溃时自动重启
支持虚拟环境运行
"""

import os
import sys
import time
import subprocess
import signal
import logging
from datetime import datetime
from pathlib import Path

class AutoRestartMonitor:
    def __init__(self):
        self.script_dir = Path(__file__).parent
        self.main_script = self.script_dir / "main.py"
        self.venv_python = self.script_dir / ".venv" / "Scripts" / "python.exe"
        self.log_dir = self.script_dir / "logs"
        self.stop_file = self.script_dir / "stop_monitor.flag"
        
        # 配置参数
        self.max_restarts = 10  # 最大重启次数
        self.restart_delay = 5  # 重启延迟（秒）
        self.restart_count = 0
        self.start_time = datetime.now()
        
        # 确保日志目录存在
        self.log_dir.mkdir(exist_ok=True)
        
        # 配置日志
        self.setup_logging()
        
        # 信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def setup_logging(self):
        """配置日志系统"""
        log_file = self.log_dir / f"monitor_{datetime.now().strftime('%Y%m%d')}.log"
        
        # 配置日志格式
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def signal_handler(self, signum, frame):
        """处理停止信号"""
        self.logger.info(f"收到停止信号 {signum}，正在优雅退出...")
        self.create_stop_flag()
        sys.exit(0)
    
    def create_stop_flag(self):
        """创建停止标志文件"""
        try:
            self.stop_file.touch()
            self.logger.info("已创建停止标志文件")
        except Exception as e:
            self.logger.error(f"创建停止标志文件失败: {e}")
    
    def should_stop(self):
        """检查是否应该停止监控"""
        return self.stop_file.exists()
    
    def check_environment(self):
        """检查运行环境"""
        self.logger.info("🔍 检查运行环境...")
        
        # 检查主脚本
        if not self.main_script.exists():
            self.logger.error(f"❌ 主脚本不存在: {self.main_script}")
            return False
        
        # 检查虚拟环境
        if not self.venv_python.exists():
            self.logger.error(f"❌ 虚拟环境Python不存在: {self.venv_python}")
            return False
        
        self.logger.info("✅ 环境检查通过")
        return True
    
    def run_main_script(self):
        """运行主脚本"""
        try:
            self.logger.info("🚀 启动主程序...")
            
            # 构建命令
            cmd = [str(self.venv_python), str(self.main_script)]
            
            # 启动进程
            process = subprocess.Popen(
                cmd,
                cwd=str(self.script_dir),
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            self.logger.info(f"✅ 主程序已启动，PID: {process.pid}")
            return process
            
        except Exception as e:
            self.logger.error(f"❌ 启动主程序失败: {e}")
            return None
    
    def monitor_process(self, process):
        """监控进程状态"""
        try:
            # 实时读取输出
            while True:
                if self.should_stop():
                    self.logger.info("检测到停止标志，终止主程序...")
                    process.terminate()
                    break
                
                # 检查进程状态
                return_code = process.poll()
                if return_code is not None:
                    # 进程已结束
                    self.logger.warning(f"⚠️ 主程序已退出，返回码: {return_code}")
                    return return_code
                
                # 读取输出（非阻塞）
                try:
                    output = process.stdout.readline()
                    if output:
                        # 将主程序输出写入日志
                        self.logger.info(f"[主程序] {output.strip()}")
                except:
                    pass
                
                time.sleep(0.1)  # 短暂休眠
            
            return 0  # 手动停止
            
        except Exception as e:
            self.logger.error(f"❌ 监控进程时出错: {e}")
            return -1
    
    def should_restart(self, return_code):
        """判断是否应该重启"""
        if self.should_stop():
            return False
        
        if self.restart_count >= self.max_restarts:
            self.logger.error(f"❌ 已达到最大重启次数 {self.max_restarts}，停止监控")
            return False
        
        if return_code == 0:
            self.logger.info("✅ 主程序正常退出，不需要重启")
            return False
        
        return True
    
    def print_status(self):
        """打印运行状态"""
        runtime = datetime.now() - self.start_time
        print("\n" + "="*60)
        print(f"📊 监控状态")
        print(f"   运行时间: {runtime}")
        print(f"   重启次数: {self.restart_count}/{self.max_restarts}")
        print(f"   日志目录: {self.log_dir}")
        print(f"   停止方法: 创建文件 {self.stop_file.name} 或按 Ctrl+C")
        print("="*60 + "\n")
    
    def run(self):
        """主监控循环"""
        self.logger.info("🎯 自动重启监控器启动")
        self.logger.info(f"   主脚本: {self.main_script}")
        self.logger.info(f"   虚拟环境: {self.venv_python}")
        
        # 检查环境
        if not self.check_environment():
            return False
        
        # 清理停止标志
        if self.stop_file.exists():
            self.stop_file.unlink()
        
        try:
            while True:
                if self.should_stop():
                    self.logger.info("检测到停止标志，退出监控")
                    break
                
                # 打印状态
                self.print_status()
                
                # 启动主程序
                process = self.run_main_script()
                if not process:
                    self.logger.error("启动失败，等待后重试...")
                    time.sleep(self.restart_delay)
                    continue
                
                # 监控进程
                return_code = self.monitor_process(process)
                
                # 判断是否需要重启
                if not self.should_restart(return_code):
                    break
                
                # 准备重启
                self.restart_count += 1
                self.logger.warning(f"🔄 准备重启 ({self.restart_count}/{self.max_restarts})...")
                self.logger.info(f"⏰ 等待 {self.restart_delay} 秒后重启...")
                time.sleep(self.restart_delay)
        
        except KeyboardInterrupt:
            self.logger.info("收到键盘中断，正在退出...")
        except Exception as e:
            self.logger.error(f"监控过程中出现错误: {e}")
        
        self.logger.info("🏁 监控器已停止")
        return True

def main():
    """主函数"""
    print("🎯 自动重启监控器")
    print("="*50)
    
    monitor = AutoRestartMonitor()
    monitor.run()

if __name__ == "__main__":
    main()
