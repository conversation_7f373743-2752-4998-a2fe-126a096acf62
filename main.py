from playwright.sync_api import sync_playwright
from playwright.sync_api import Page, expect
import re
import time
import difflib
from typing import List, Tu<PERSON>, Optional
from ocr_handler import 识别图片内容, 清理临时文件

playwright = None
browser = None
page = None

答案 = {}

def 检测图片内容(html_content: str) -> List[str]:
    """检测HTML内容中的图片URL"""
    img_urls = []
    # 使用正则表达式查找所有img标签的src属性
    img_pattern = r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>'
    matches = re.findall(img_pattern, html_content, re.IGNORECASE)

    for url in matches:
        if url.startswith('http'):
            img_urls.append(url)
            print(f"🖼️ 发现图片: {url}")

    return img_urls

def 下载并识别图片(img_url: str) -> str:
    """下载图片并使用OCR识别内容"""
    return 识别图片内容(img_url)

def 获取混合内容(page_locator) -> Tuple[str, List[str]]:
    """获取包含文本和图片的混合内容"""
    try:
        # 获取纯文本内容
        text_content = page_locator.inner_text()

        # 获取HTML内容以检测图片
        html_content = page_locator.inner_html()

        # 检测图片
        img_urls = 检测图片内容(html_content)

        # 识别图片内容
        img_contents = []
        for img_url in img_urls:
            img_content = 下载并识别图片(img_url)
            img_contents.append(img_content)

        # 合并文本和图片内容
        combined_content = text_content
        if img_contents:
            combined_content += " " + " ".join(img_contents)

        return combined_content, img_urls

    except Exception as e:
        print(f"❌ 获取混合内容失败: {e}")
        return page_locator.inner_text(), []

def 清理文本(text: str) -> str:
    """清理文本，去除多余的空格、换行符和标点符号"""
    if not text:
        return ""

    # 去除首尾空格和换行符
    cleaned = text.strip()

    # 将多个连续空格替换为单个空格
    cleaned = re.sub(r'\s+', ' ', cleaned)

    # 去除常见的标点符号，特别处理顿号和逗号
    cleaned = re.sub(r'[。、，；：！？""''（）【】《》〈〉\s]', '', cleaned)

    return cleaned.strip()

def 提取答案选项(answer_text: str) -> List[str]:
    """从答案文本中提取可能的选项，避免重复和选项字母"""
    if not answer_text:
        return []

    # 尝试按常见分隔符分割
    options = []

    # 按换行符分割
    lines = answer_text.split('\n')
    for line in lines:
        line = line.strip()
        if line and len(line) > 2:
            # 去除选项字母前缀 (A、B、C、D等)
            cleaned_line = re.sub(r'^[A-D][、\s\.]\s*', '', line)
            if cleaned_line:
                options.append(清理文本(cleaned_line))

    # 如果没有找到多行，尝试按其他分隔符
    if len(options) <= 1:
        # 按句号分割
        parts = answer_text.split('。')
        for part in parts:
            part = part.strip()
            if part and len(part) > 2:
                # 去除选项字母前缀
                cleaned_part = re.sub(r'^[A-D][、\s\.]\s*', '', part)
                if cleaned_part:
                    options.append(清理文本(cleaned_part))

    # 去重并过滤，避免重复选项
    unique_options = []
    for opt in options:
        if opt and len(opt) > 2:
            # 检查是否已存在相似选项
            is_duplicate = False
            for existing in unique_options:
                if opt in existing or existing in opt or difflib.SequenceMatcher(None, opt, existing).ratio() > 0.9:
                    is_duplicate = True
                    break
            if not is_duplicate:
                unique_options.append(opt)

    return unique_options

def 智能匹配答案(答案选项列表: List[str], 页面选项列表: List[str]) -> Tuple[Optional[int], str]:
    """
    智能匹配答案，返回匹配的选项索引和匹配原因
    返回: (选项索引(0-based), 匹配原因)
    """
    if not 答案选项列表 or not 页面选项列表:
        return None, "答案或选项列表为空"

    print(f"🔍 开始智能匹配...")
    print(f"答案选项: {答案选项列表}")
    print(f"页面选项: {页面选项列表}")

    # 策略1: 精确匹配（清理后）
    for i, 页面选项 in enumerate(页面选项列表):
        清理后页面选项 = 清理文本(页面选项)
        for 答案选项 in 答案选项列表:
            if 清理后页面选项 == 答案选项:
                return i, f"精确匹配: '{答案选项}'"

    # 策略2: 包含匹配（答案包含在选项中）
    for i, 页面选项 in enumerate(页面选项列表):
        清理后页面选项 = 清理文本(页面选项)
        for 答案选项 in 答案选项列表:
            if 答案选项 in 清理后页面选项 or 清理后页面选项 in 答案选项:
                return i, f"包含匹配: 答案'{答案选项}' <-> 选项'{清理后页面选项}'"

    # 策略3: 关键词匹配（提取关键词进行匹配）
    for i, 页面选项 in enumerate(页面选项列表):
        清理后页面选项 = 清理文本(页面选项)
        for 答案选项 in 答案选项列表:
            # 提取长度大于2的词作为关键词
            答案关键词 = [word for word in re.findall(r'[\u4e00-\u9fff]+', 答案选项) if len(word) > 2]
            选项关键词 = [word for word in re.findall(r'[\u4e00-\u9fff]+', 清理后页面选项) if len(word) > 2]

            # 检查是否有共同关键词
            共同关键词 = set(答案关键词) & set(选项关键词)
            if 共同关键词 and len(共同关键词) >= 1:
                return i, f"关键词匹配: 共同关键词 {共同关键词}"

    # 策略4: 相似度匹配
    最高相似度 = 0
    最佳匹配索引 = None
    最佳匹配信息 = ""

    for i, 页面选项 in enumerate(页面选项列表):
        清理后页面选项 = 清理文本(页面选项)
        for 答案选项 in 答案选项列表:
            相似度 = difflib.SequenceMatcher(None, 答案选项, 清理后页面选项).ratio()
            if 相似度 > 最高相似度:
                最高相似度 = 相似度
                最佳匹配索引 = i
                最佳匹配信息 = f"相似度匹配: {相似度:.2f} - 答案'{答案选项}' <-> 选项'{清理后页面选项}'"

    # 如果相似度大于0.6，认为是有效匹配
    if 最高相似度 > 0.6:
        return 最佳匹配索引, 最佳匹配信息

    return None, f"所有匹配策略都失败，最高相似度: {最高相似度:.2f}"

课程 = [
    "语文(上)",
    "语文(下)",
    "数学(下)",
    "沟通技巧",
    "就业指导",
    "职业健康与安全",
    "数学(上)",
    "多媒体技术基础",
    "Office办公软件",
    "办公设备使用与维护",
    "职业应用写作",
    "历史",
    "个人与团队",
    "Windows操作系统",
    "学习指南",
    "计算机职业素养",
    "计算机常用工具软件",
    "计算机网络基础",
    "中国特色社会主义",
    "职业道德与法治",
    "哲学与人生",
    "心理健康与职业生涯",
    "信息技术"
]



def init_playwright(port=6666):
    """初始化 Playwright 和浏览器连接"""
    global playwright, browser, page

    try:
        # 启动 Playwright
        playwright = sync_playwright().start()

        # 尝试连接现有浏览器
        browser = playwright.chromium.connect_over_cdp(f"http://localhost:{port}")

        # 获取页面
        context = browser.contexts[0] if browser.contexts else browser.new_context()
        page = context.pages[0] if context.pages else context.new_page()
        page.bring_to_front()

        #page.pause()  # 进入录制模式

        print(f"已连接到页面: {page.url}")
        return True
    except Exception as e:
        print(f"连接失败: {e}")
        return False


def 导航到课程详情():
        global page, browser, playwright
        # 示例：在附加后执行操作
        page.goto("https://zydz-menhu.ouchn.edu.cn/learningPlatformH5/home")
        page.get_by_text("查看全部").click()
        try:
            for i in range(1, 50):
                e = page.locator(f"div:nth-child({i}) > .module_course_info_container > .progressView > .linear_container > .linear_content > .bubble")
                t = e.inner_text()
                if t != "100%":
                    for j in 课程:
                        if page.locator(f"div:nth-child({i}) > .module_course_info_container > .majorName > div > span").inner_text() == j:
                            print(f"进入课程: {j}")
                            with page.expect_navigation():
                                page.locator(f"div:nth-child({i}) > .module_course_info_container > .majorName > div > span").click()
                            return True

        except Exception as e:
            print(f"默认50，不是致命错误: {e}")
            重置链接()

def 检测链接():
    global page, browser, playwright
    重置链接()
    print(f"当前链接: {page.url}")
    return page.url

def 重置链接():
    global playwright, browser, page, 答案
    browser.close()
    playwright.stop()
    playwright = None
    browser = None
    page = None
    init_playwright()
    答案 = {}
    # 清理OCR临时文件
    清理临时文件()


def 总检测逻辑():
    global page, browser, playwright
    链接 = 检测链接()
    if "https://zydz-menhu.ouchn.edu.cn/learningPlatformH5/courseStudy" in 链接:
        print("成功进入课程详情")
        print("尝试进入视频或答题页面")
        导航到视频或答题页面()
        总检测逻辑()
    elif "https://zydz-menhu.ouchn.edu.cn/learningPlatformH5/videoStudy" in 链接:
        print("成功进入视频页面")
        视频处理()
        导航到课程详情()
        总检测逻辑()
    elif "https://zydz-menhu.ouchn.edu.cn/learningPlatformH5/exam/answer" in 链接:
        print("成功进入答题页面")
        获取答案前的步骤()
        总检测逻辑()
    elif "https://zydz-menhu.ouchn.edu.cn/learningPlatformH5/exam/testPaper" in 链接:
        print("成功进入答案页面")
        获取答案()
        导航到课程详情()
        导航到视频或答题页面()
        print("开始填写答案")
        答题()
        导航到课程详情()
        总检测逻辑()
    else:
        print("尝试进入课程详情")
        导航到课程详情()
        总检测逻辑()



def 导航到视频或答题页面():
    global page, browser, playwright
    try:
        for i in range(1, 50):
            if page.locator(f"#van-tab-2 > div > div > div > div:nth-child({i}) > div > div > span.title_vice").inner_text() == "未完成":
                page.locator(f"#van-tab-2 > div > div > div > div:nth-child({i}) > div > div > span.title_vice").click()
                for k in range(1, 50):
                    page.locator(f"#van-tab-2 > div > div > div > div:nth-child({i}) > div.van-collapse-item__wrapper > div > div:nth-child({k}) > div > div > div").click()
                    for l in range(1, 50):
                        for m in range(1, 50):
                            if page.locator(f"#van-tab-2 > div > div > div > div:nth-child({l}) > div.van-collapse-item__wrapper > div > div:nth-child({m}) > div > div.van-collapse-item__wrapper > div > div > div > div.flex.itemTitle > span").count() > 0:
                                if page.locator(f"#van-tab-2 > div > div > div > div:nth-child({l}) > div.van-collapse-item__wrapper > div > div:nth-child({m}) > div > div.van-collapse-item__wrapper > div > div > div > div.flex.itemTitle > span").inner_text() != "已完成":
                                    #print("1")
                                    page.locator(f"#van-tab-2 > div > div > div > div:nth-child({l}) > div.van-collapse-item__wrapper > div > div:nth-child({m}) > div > div.van-collapse-item__wrapper > div > div > div > div.flex.itemTitle > span").click()
                                    return True
                            elif page.locator(f"#van-tab-2 > div > div > div > div:nth-child({l}) > div.van-collapse-item__wrapper > div > div:nth-child({m}) > div > div.van-collapse-item__wrapper > div > div > div > div > span").count() > 0:
                                if page.locator(f"#van-tab-2 > div > div > div > div:nth-child({l}) > div.van-collapse-item__wrapper > div > div:nth-child({m}) > div > div.van-collapse-item__wrapper > div > div > div > div > span").inner_text() != "已完成":
                                    #print("2")
                                    page.locator(f"#van-tab-2 > div > div > div > div:nth-child({l}) > div.van-collapse-item__wrapper > div > div:nth-child({m}) > div > div.van-collapse-item__wrapper > div > div > div > div > span").click()
                                    return True
                            else:
                                #print("0")
                                break


    except Exception as e:
        print(f"可能当前课程已完成: {e}")
        print("重新导航")
        导航到课程详情()

def 视频处理():
    global page, browser, playwright
    while True:
        time.sleep(3)
        a = page.query_selector('//*[@id="xgPlayer"]/xg-controls/xg-inner-controls/xg-left-grid/xg-icon[2]/span[1]/span[1]').text_content()
        b = page.query_selector('//*[@id="xgPlayer"]/xg-controls/xg-inner-controls/xg-left-grid/xg-icon[2]/span[1]/span[2]').text_content()
        ab = a+":"+b
        c = page.query_selector("#xgPlayer > xg-controls > xg-inner-controls > xg-left-grid > xg-icon.xgplayer-time > span.time-duration").text_content()
        if c == ab:
            print("视频播放完成")
            time.sleep(3)
            return True
        print(f"当前播放时间: {ab} -> {c}")

def 获取答案前的步骤():
    global page, browser, playwright
    print("开始处理获取答案前的步骤")
    time.sleep(3)
    if page.locator('#app > div > div.layout-content.noShowTab > div > div.answerContent > div.answerTitle').count() == 0:
        print("❌ 未找到答案区域")
        print("尝试刷新")
        page.locator('#app > div > div.layout-content.noShowTab > div > div.questionBtn > div:nth-child(2)').click()
        page.locator('#app > div > div.layout-content.noShowTab > div > div.questionBtn > div:nth-child(1)').click()

    p = page.locator("#app > div > div.layout-content.noShowTab > div.answer > div.answerContent > div.subject > div.curAnser").inner_text()
    c = re.search(r'/(\d+)', p)
    print("总题目数："+c.group(1))
    for i in range(1, int(c.group(1)) + 1):
        page.locator(f"#app > div > div.layout-content.noShowTab > div.answer > div.answerContent > div.amswerOption > div:nth-child(1) > div.optionIndex").click()
        page.locator("#app > div > div.layout-content.noShowTab > div.answer > div.questionBtn > div:nth-child(2)").click()
    page.locator("#app > div > div.layout-content.noShowTab > div.answer > div.pageEnd > div").click()
    page.locator("#app > div > div.layout-content.noShowTab > div.van-overlay > div > div > div.dialog-footer > button.van-button.van-button--default.van-button--large.determine").click()
    time.sleep(3)
    page.locator("#app > div > div.layout-content.noShowTab > div.van-overlay > div > div > div.dialog-footer > button > div > span").click()

def 获取答案():
    global page, browser, playwright, 答案
    print("🔄 开始获取答案")
    p = page.locator("#app > div > div.layout-content.noShowTab > div.answer > div.answerContent > div.subject > div.curAnser").inner_text()
    c = re.search(r'/(\d+)', p)
    print("总题目数：" + c.group(1))
    if page.locator('#app > div > div.layout-content.noShowTab > div > div.answerContent > div.answerTitle').count() == 0:
        print("❌ 未找到答案区域")
        print("尝试刷新")
        page.locator('#app > div > div.layout-content.noShowTab > div > div.questionBtn > div:nth-child(2)').click()
        page.locator('#app > div > div.layout-content.noShowTab > div > div.questionBtn > div:nth-child(1)').click()

    for i in range(1, int(c.group(1)) + 1):
        # 获取题目
        f = page.locator('#app > div > div.layout-content.noShowTab > div > div.answerContent > div.answerTitle').inner_text()

        # 获取答案区域的混合内容（文本+图片）
        answer_locator = page.locator('.everyOption.answerSort')
        x, img_urls = 获取混合内容(answer_locator)

        print(f"\n📝 第{i}题题目: {f}")
        print(f"📋 原始答案文本: {repr(x)}")
        if img_urls:
            print(f"🖼️ 包含图片: {len(img_urls)} 张")
            for idx, url in enumerate(img_urls, 1):
                print(f"   图片{idx}: {url}")

        # 改进的答案提取逻辑
        if x:
            # 尝试多种方式提取答案
            答案选项 = 提取答案选项(x)

            if not 答案选项:
                # 如果提取失败，使用原来的方法作为备选
                pattern = r'\n(.*)'
                g = re.search(pattern, x, re.DOTALL)
                if g:
                    fallback_answer = 清理文本(g.group(1))
                    答案选项 = [fallback_answer] if fallback_answer else []

            print(f"✅ 提取的答案选项: {答案选项}")
            答案[f] = 答案选项
        else:
            print(f"❌ 未能获取到答案文本")
            答案[f] = []

        # 点击下一题
        page.locator("#app > div > div.layout-content.noShowTab > div.answer > div.questionBtn > div:nth-child(2)").click()

    print(f"\n📚 所有答案获取完成: {len(答案)} 题")
    for 题目, 选项 in 答案.items():
        print(f"  {题目}: {选项}")

def 答题():
    global page, browser, playwright, 答案
    print("🎯 开始智能答题")
    p = page.locator("#app > div > div.layout-content.noShowTab > div.answer > div.answerContent > div.subject > div.curAnser").inner_text()
    c = re.search(r'/(\d+)', p)
    print("总题目数：" + c.group(1))
    if page.locator('#app > div > div.layout-content.noShowTab > div > div.answerContent > div.answerTitle').count() == 0:
        print("❌ 未找到答案区域")
        print("尝试刷新")
        page.locator('#app > div > div.layout-content.noShowTab > div > div.questionBtn > div:nth-child(2)').click()
        page.locator('#app > div > div.layout-content.noShowTab > div > div.questionBtn > div:nth-child(1)').click()

    for i in range(1, int(c.group(1)) + 1):
        f = page.locator('#app > div > div.layout-content.noShowTab > div.answer > div.answerContent > div.answerTitle').inner_text()
        try:
            print(f"\n📝 第{i}题题目：{f}")
            print(f"📋 存储的答案选项：{答案.get(f, [])}")

            下一题 = "#app > div > div.layout-content.noShowTab > div.answer > div.questionBtn > div:nth-child(2)"

            # 定义所有选项的选择器
            选项选择器列表 = [
                "#app > div > div.layout-content.noShowTab > div.answer > div.answerContent > div.amswerOption > div:nth-child(1) > div.selectContent",
                "#app > div > div.layout-content.noShowTab > div.answer > div.answerContent > div.amswerOption > div:nth-child(2) > div.selectContent",
                "#app > div > div.layout-content.noShowTab > div.answer > div.answerContent > div.amswerOption > div:nth-child(3) > div.selectContent",
                "#app > div > div.layout-content.noShowTab > div.answer > div.answerContent > div.amswerOption > div:nth-child(4) > div.selectContent"
            ]

            # 获取页面上的所有选项内容
            页面选项列表 = []
            有效选择器列表 = []

            for 选项选择器 in 选项选择器列表:
                if page.locator(选项选择器).count() > 0:
                    # 获取混合内容（文本+图片）
                    选项内容, 选项图片 = 获取混合内容(page.locator(选项选择器))
                    页面选项列表.append(选项内容)
                    有效选择器列表.append(选项选择器)

                    print(f"选项{len(页面选项列表)}: {repr(选项内容)}")
                    if 选项图片:
                        print(f"   包含图片: {len(选项图片)} 张")

            # 使用智能匹配算法
            if f in 答案 and 答案[f]:
                匹配结果, 匹配原因 = 智能匹配答案(答案[f], 页面选项列表)

                if 匹配结果 is not None:
                    print(f"✅ {匹配原因}")
                    print(f"点击选项{匹配结果 + 1}")

                    # 点击匹配的选项
                    page.locator(有效选择器列表[匹配结果]).click()
                    page.locator(下一题).click()
                else:
                    print(f"❌ 智能匹配失败: {匹配原因}")
                    print("默认选择第一个选项")
                    if 有效选择器列表:
                        page.locator(有效选择器列表[0]).click()
                    page.locator(下一题).click()
            else:
                print(f"❌ 题目'{f}'没有找到对应答案")
                print("默认选择第一个选项")
                if 有效选择器列表:
                    page.locator(有效选择器列表[0]).click()
                page.locator(下一题).click()

        except Exception as e:
            print(f"❌ 第{i}题处理失败: {e}")
            print("重试整个流程")
            总检测逻辑()

    print("交卷")
    page.locator("#app > div > div.layout-content.noShowTab > div.answer > div.pageEnd > div").click()
    page.locator("#app > div > div.layout-content.noShowTab > div.van-overlay > div > div > div.dialog-footer > button.van-button.van-button--default.van-button--large.determine").click()
    time.sleep(3)
    zz = page.locator("#app > div > div.layout-content.noShowTab > div.van-overlay > div > div > div.dialogContent > div.errorReson").inner_text()
    print(f"答题结果：{zz}")
    page.locator("#app > div > div.layout-content.noShowTab > div.van-overlay > div > div > div.dialog-footer > button > div > span").click()


if __name__ == "__main__":
    init_playwright()
    page.goto("https://zydz-menhu.ouchn.edu.cn/learningPlatformH5/home")
    总检测逻辑()

    print(page.url)

