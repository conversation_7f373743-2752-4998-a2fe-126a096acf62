@echo off
title Auto Restart Monitor

echo.
echo ========================================
echo Auto Restart Monitor
echo ========================================
echo.

:: Check Python environment
if not exist ".venv\Scripts\python.exe" (
    echo ERROR: Virtual environment not found
    echo Please create virtual environment first:
    echo    python -m venv .venv
    pause
    exit /b 1
)

:: Check main script
if not exist "main.py" (
    echo ERROR: main.py not found
    pause
    exit /b 1
)

:: Start monitor
echo Starting monitor...
echo.
echo Tips:
echo    - Press Ctrl+C to stop monitoring
echo    - Or create stop_monitor.flag file to stop
echo    - Logs are saved in logs directory
echo.

.venv\Scripts\python.exe auto_restart.py

echo.
echo Monitor stopped
pause
