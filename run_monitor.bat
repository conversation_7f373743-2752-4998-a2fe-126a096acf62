@echo off
title Auto Restart Monitor

echo ========================================
echo Auto Restart Monitor for main.py
echo ========================================
echo.

if not exist ".venv\Scripts\python.exe" (
    echo ERROR: Virtual environment not found
    echo Please create it first: python -m venv .venv
    pause
    exit /b 1
)

if not exist "main.py" (
    echo ERROR: main.py not found
    pause
    exit /b 1
)

echo Starting monitor...
echo Press Ctrl+C to stop
echo.

.venv\Scripts\python.exe auto_restart.py

echo.
echo Monitor stopped
pause
